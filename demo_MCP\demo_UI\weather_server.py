#!/usr/bin/env python3
"""
天气查询MCP服务器
提供天气查询工具，支持通过城市名称查询当前天气信息
"""

import json
import random
from datetime import datetime
from mcp.server.fastmcp import FastMCP

# 创建 MCP 服务
mcp = FastMCP('weather_service')

# 模拟天气数据（实际应用中可以接入真实的天气API）
WEATHER_CONDITIONS = [
    "晴朗", "多云", "阴天", "小雨", "中雨", "大雨", 
    "雷阵雨", "雪", "雾", "霾", "沙尘暴"
]

CITIES_DATA = {
    "北京": {"lat": 39.9042, "lon": 116.4074, "timezone": "Asia/Shanghai"},
    "上海": {"lat": 31.2304, "lon": 121.4737, "timezone": "Asia/Shanghai"},
    "广州": {"lat": 23.1291, "lon": 113.2644, "timezone": "Asia/Shanghai"},
    "深圳": {"lat": 22.3193, "lon": 114.1694, "timezone": "Asia/Shanghai"},
    "杭州": {"lat": 30.2741, "lon": 120.1551, "timezone": "Asia/Shanghai"},
    "成都": {"lat": 30.5728, "lon": 104.0668, "timezone": "Asia/Shanghai"},
    "西安": {"lat": 34.3416, "lon": 108.9398, "timezone": "Asia/Shanghai"},
    "武汉": {"lat": 30.5928, "lon": 114.3055, "timezone": "Asia/Shanghai"},
    "重庆": {"lat": 29.5630, "lon": 106.5516, "timezone": "Asia/Shanghai"},
    "天津": {"lat": 39.3434, "lon": 117.3616, "timezone": "Asia/Shanghai"},
}

def generate_mock_weather(city: str) -> dict:
    """生成模拟天气数据"""
    if city not in CITIES_DATA:
        return None
    
    city_info = CITIES_DATA[city]
    current_time = datetime.now()
    
    # 生成随机天气数据
    temperature = random.randint(-10, 35)
    humidity = random.randint(30, 90)
    wind_speed = random.randint(0, 20)
    condition = random.choice(WEATHER_CONDITIONS)
    
    return {
        "city": city,
        "country": "中国",
        "latitude": city_info["lat"],
        "longitude": city_info["lon"],
        "timezone": city_info["timezone"],
        "current_time": current_time.strftime("%Y-%m-%d %H:%M:%S"),
        "weather": {
            "condition": condition,
            "temperature": temperature,
            "feels_like": temperature + random.randint(-3, 3),
            "humidity": humidity,
            "wind_speed": wind_speed,
            "wind_direction": random.choice(["北", "南", "东", "西", "东北", "西北", "东南", "西南"]),
            "pressure": random.randint(1000, 1030),
            "visibility": random.randint(5, 20),
            "uv_index": random.randint(1, 11)
        },
        "forecast": {
            "description": f"今天{condition}，温度{temperature}°C，湿度{humidity}%，风速{wind_speed}km/h"
        }
    }

@mcp.tool()
def get_current_weather(city: str) -> str:
    """
    获取指定城市的当前天气信息
    
    Args:
        city: 城市名称（如：北京、上海、广州等）
    
    Returns:
        包含天气信息的JSON字符串，包括温度、湿度、风速、天气状况等
    """
    try:
        weather_data = generate_mock_weather(city)
        
        if weather_data is None:
            return json.dumps({
                "error": f"不支持的城市: {city}",
                "supported_cities": list(CITIES_DATA.keys())
            }, ensure_ascii=False, indent=2)
        
        return json.dumps(weather_data, ensure_ascii=False, indent=2)
    
    except Exception as e:
        return json.dumps({
            "error": f"获取天气信息时发生错误: {str(e)}"
        }, ensure_ascii=False, indent=2)

@mcp.tool()
def get_weather_forecast(city: str, days: int = 3) -> str:
    """
    获取指定城市的天气预报
    
    Args:
        city: 城市名称（如：北京、上海、广州等）
        days: 预报天数（1-7天，默认3天）
    
    Returns:
        包含未来几天天气预报的JSON字符串
    """
    try:
        if city not in CITIES_DATA:
            return json.dumps({
                "error": f"不支持的城市: {city}",
                "supported_cities": list(CITIES_DATA.keys())
            }, ensure_ascii=False, indent=2)
        
        if days < 1 or days > 7:
            days = 3
        
        forecast_data = {
            "city": city,
            "forecast_days": days,
            "forecasts": []
        }
        
        for i in range(days):
            date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            date = date.replace(day=date.day + i)
            
            temp_high = random.randint(15, 35)
            temp_low = random.randint(-5, temp_high - 5)
            condition = random.choice(WEATHER_CONDITIONS)
            
            forecast_data["forecasts"].append({
                "date": date.strftime("%Y-%m-%d"),
                "day_of_week": ["周一", "周二", "周三", "周四", "周五", "周六", "周日"][date.weekday()],
                "condition": condition,
                "temperature_high": temp_high,
                "temperature_low": temp_low,
                "humidity": random.randint(30, 90),
                "wind_speed": random.randint(0, 15),
                "precipitation_chance": random.randint(0, 100)
            })
        
        return json.dumps(forecast_data, ensure_ascii=False, indent=2)
    
    except Exception as e:
        return json.dumps({
            "error": f"获取天气预报时发生错误: {str(e)}"
        }, ensure_ascii=False, indent=2)

@mcp.tool()
def list_supported_cities() -> str:
    """
    获取支持查询天气的城市列表
    
    Returns:
        支持的城市列表JSON字符串
    """
    return json.dumps({
        "supported_cities": list(CITIES_DATA.keys()),
        "total_count": len(CITIES_DATA),
        "note": "目前支持中国主要城市的天气查询"
    }, ensure_ascii=False, indent=2)

if __name__ == "__main__":
    # 以标准 I/O 方式运行 MCP 服务器
    print("启动天气查询MCP服务器...")
    mcp.run(transport='stdio')
