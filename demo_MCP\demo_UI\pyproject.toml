[project]
name = "weather-agent-ui"
version = "0.1.0"
description = "Weather query agent with LangGraph and FastHTML UI"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "langgraph>=0.2.0",
    "langchain>=0.3.0",
    "langchain-openai>=0.2.0",
    "fasthtml>=0.6.0",
    "mcp>=1.12.2",
    "httpx>=0.27.0",
    "uvicorn>=0.30.0",
    "python-dotenv>=1.0.0",
    "requests>=2.31.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src"]
