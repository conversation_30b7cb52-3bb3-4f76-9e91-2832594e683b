# 航天质量风险管理智能体

基于LangGraph框架和MCP协议的智能天气查询系统，提供FastHTML Web界面进行交互。

## 🚀 项目特性

- **LangGraph智能体**: 使用LangGraph框架构建的智能对话代理
- **MCP协议集成**: 通过MCP (Model Context Protocol) 调用天气查询工具
- **FastHTML UI**: 现代化的Web界面，支持实时对话
- **天气查询功能**: 支持全国主要城市的天气查询和预报
- **航天风险分析**: 从航天质量风险管理角度分析天气条件影响

## 📁 项目结构

```
demo_UI/
├── pyproject.toml          # 项目配置和依赖
├── .env.example           # 环境变量示例
├── .env                   # 环境变量配置（需要创建）
├── weather_server.py      # MCP天气查询服务器
├── agent.py              # LangGraph智能体
├── app.py                # FastHTML Web应用
├── run.py                # 启动脚本
└── README.md             # 项目说明文档
```

## 🛠️ 环境要求

- Python 3.11+
- Conda环境: `langgraph-env`
- OpenAI API密钥（用于LLM功能）

## 📦 依赖包

主要依赖包包括：
- `langgraph>=0.2.0` - 智能体框架
- `langchain>=0.3.0` - LLM集成
- `langchain-openai>=0.2.0` - OpenAI集成
- `fasthtml>=0.6.0` - Web界面框架
- `mcp>=1.12.2` - MCP协议支持
- `httpx>=0.27.0` - HTTP客户端
- `uvicorn>=0.30.0` - ASGI服务器
- `python-dotenv>=1.0.0` - 环境变量管理

## 🚀 快速开始

### 1. 环境准备

确保您在正确的conda环境中：

```bash
conda activate langgraph-env
```

### 2. 安装依赖

```bash
cd demo_UI
pip install -e .
```

或者手动安装依赖：

```bash
pip install langgraph langchain langchain-openai fasthtml mcp httpx uvicorn python-dotenv requests
```

### 3. 配置环境变量

复制环境变量示例文件：

```bash
# Windows
copy .env.example .env

# Linux/macOS
cp .env.example .env
```

编辑 `.env` 文件，设置您的API密钥：

```env
# OpenAI API配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1

# 应用配置
HOST=127.0.0.1
PORT=8000
DEBUG=True
```

### 4. 启动应用

使用启动脚本（推荐）：

```bash
python run.py
```

或者直接启动：

```bash
python app.py
```

### 5. 访问应用

打开浏览器访问：http://127.0.0.1:8000

## 🎯 使用说明

### 天气查询功能

支持的城市：北京、上海、广州、深圳、杭州、成都、西安、武汉、重庆、天津

示例查询：
- "北京今天天气怎么样？"
- "上海的温度是多少？"
- "广州未来三天的天气预报"
- "深圳的湿度和风速如何？"

### 智能对话

智能体会：
1. 自动识别天气查询需求
2. 提取城市名称
3. 调用MCP天气服务
4. 从航天质量风险管理角度分析天气影响
5. 提供专业的回答和建议

## 🏗️ 架构说明

### 系统架构

```
用户界面 (FastHTML)
    ↓
LangGraph智能体 (agent.py)
    ↓
MCP客户端
    ↓
MCP服务器 (weather_server.py)
    ↓
天气数据源 (模拟数据)
```

### 工作流程

1. **用户输入**: 用户在Web界面输入问题
2. **意图识别**: LangGraph智能体分析用户意图
3. **工具调用**: 如需天气信息，通过MCP协议调用天气服务
4. **数据处理**: MCP服务器返回天气数据
5. **响应生成**: 智能体基于数据生成专业回答
6. **界面展示**: FastHTML界面展示对话结果

### MCP协议

本项目使用MCP (Model Context Protocol) 实现工具调用：
- **服务器端**: `weather_server.py` 提供天气查询工具
- **客户端**: `agent.py` 中的智能体调用MCP工具
- **通信方式**: 标准I/O (stdio) 传输

## 🔧 开发说明

### 添加新的天气功能

在 `weather_server.py` 中添加新的MCP工具：

```python
@mcp.tool()
def new_weather_function(param: str) -> str:
    """
    新的天气功能描述
    """
    # 实现逻辑
    return result
```

### 修改智能体行为

在 `agent.py` 中修改LangGraph工作流：

```python
def _build_graph(self) -> StateGraph:
    workflow = StateGraph(AgentState)
    # 添加新节点和边
    return workflow.compile()
```

### 自定义UI界面

在 `app.py` 中修改FastHTML组件和样式。

## 🐛 故障排除

### 常见问题

1. **MCP服务器启动失败**
   - 检查Python路径和权限
   - 确保所有依赖已正确安装

2. **OpenAI API调用失败**
   - 检查API密钥是否正确设置
   - 确认网络连接和API配额

3. **Web界面无法访问**
   - 检查端口是否被占用
   - 确认防火墙设置

4. **依赖包安装失败**
   - 更新pip: `pip install --upgrade pip`
   - 使用国内镜像: `pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/`

### 调试模式

启用调试模式查看详细日志：

```env
DEBUG=True
```

## 📄 许可证

本项目仅供学习和研究使用。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

## 📞 联系方式

如有问题或建议，请通过GitHub Issues联系。
