#!/usr/bin/env python3
"""
简单的FastHTML应用
"""

from fasthtml.common import *
import uvicorn

# 创建FastHTML应用
app, rt = fast_app()

@rt("/")
def homepage():
    """主页"""
    return Html(
        Head(Title("航天质量风险管理智能体")),
        Body(
            H1("🚀 航天质量风险管理智能体"),
            P(""),
            P("应用正在运行中..."),
            A("测试链接", href="/test")
        )
    )

@rt("/test")
def test_page():
    """测试页面"""
    return Html(
        Head(Title("测试页面")),
        Body(
            H1("测试页面"),
            P("这是一个测试页面"),
            A("返回首页", href="/")
        )
    )

if __name__ == "__main__":
    print("启动简单应用...")
    print("访问地址: http://127.0.0.1:0 (系统自动分配端口)")
    uvicorn.run(app, host="127.0.0.1", port=0)
