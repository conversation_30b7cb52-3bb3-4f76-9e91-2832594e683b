#!/usr/bin/env python3
"""
LangGraph智能体
集成MCP客户端，提供天气查询功能的智能对话代理
"""

import os
import json
import asyncio
import subprocess
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from langchain_openai import ChatOpenAI
from langchain.schema import HumanMessage, AIMessage, SystemMessage
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from typing_extensions import Annotated, TypedDict

# MCP客户端相关导入
import httpx
try:
    from mcp import ClientSession, StdioServerParameters
    from mcp.client.stdio import stdio_client
except ImportError:
    print("警告: MCP客户端导入失败，将使用模拟模式")

@dataclass
class WeatherAgentConfig:
    """智能体配置"""
    openai_api_key: str
    openai_base_url: str = "https://api.openai.com/v1"
    model_name: str = "gpt-3.5-turbo"
    temperature: float = 0.7
    max_tokens: int = 1000

class AgentState(TypedDict):
    """智能体状态"""
    messages: Annotated[List, add_messages]
    user_input: str
    weather_data: Optional[str]
    response: str

class WeatherAgent:
    """天气查询智能体"""
    
    def __init__(self, config: WeatherAgentConfig):
        self.config = config
        self.llm = ChatOpenAI(
            api_key=config.openai_api_key,
            base_url=config.openai_base_url,
            model=config.model_name,
            temperature=config.temperature,
            max_tokens=config.max_tokens
        )
        self.mcp_session = None
        self.graph = self._build_graph()
    
    def _build_graph(self) -> StateGraph:
        """构建LangGraph工作流"""
        workflow = StateGraph(AgentState)
        
        # 添加节点
        workflow.add_node("analyze_input", self._analyze_input)
        workflow.add_node("call_weather_tool", self._call_weather_tool)
        workflow.add_node("generate_response", self._generate_response)
        
        # 设置入口点
        workflow.set_entry_point("analyze_input")
        
        # 添加边
        workflow.add_conditional_edges(
            "analyze_input",
            self._should_call_weather_tool,
            {
                "weather": "call_weather_tool",
                "direct": "generate_response"
            }
        )
        
        workflow.add_edge("call_weather_tool", "generate_response")
        workflow.add_edge("generate_response", END)
        
        return workflow.compile()
    
    async def _start_mcp_server(self):
        """启动MCP服务器"""
        try:
            # 简化实现：直接调用天气查询函数而不是通过MCP
            print("使用简化的天气查询实现")
            return True

        except Exception as e:
            print(f"启动MCP服务器失败: {e}")
            return False
    
    async def _call_mcp_tool(self, tool_name: str, arguments: Dict[str, Any]) -> str:
        """调用MCP工具（简化实现）"""
        try:
            # 简化实现：直接导入并调用天气服务器的函数
            from weather_server import get_current_weather, get_weather_forecast, list_supported_cities

            if tool_name == "get_current_weather":
                return get_current_weather(arguments.get("city", "北京"))
            elif tool_name == "get_weather_forecast":
                return get_weather_forecast(arguments.get("city", "北京"), arguments.get("days", 3))
            elif tool_name == "list_supported_cities":
                return list_supported_cities()
            else:
                return f"未知的工具: {tool_name}"

        except Exception as e:
            print(f"调用MCP工具失败: {e}")
            return f"调用工具时发生错误: {str(e)}"
    
    def _analyze_input(self, state: AgentState) -> AgentState:
        """分析用户输入"""
        user_input = state.get("user_input", "")
        
        # 简单的关键词检测来判断是否需要天气查询
        weather_keywords = ["天气", "气温", "温度", "下雨", "晴天", "阴天", "风", "湿度", "预报"]
        needs_weather = any(keyword in user_input for keyword in weather_keywords)
        
        state["needs_weather"] = needs_weather
        return state
    
    def _should_call_weather_tool(self, state: AgentState) -> str:
        """判断是否需要调用天气工具"""
        return "weather" if state.get("needs_weather", False) else "direct"
    
    async def _call_weather_tool(self, state: AgentState) -> AgentState:
        """调用天气查询工具"""
        user_input = state.get("user_input", "")
        
        # 使用LLM提取城市名称
        extract_prompt = f"""
        从以下用户输入中提取城市名称，只返回城市名称，不要其他内容：
        用户输入：{user_input}
        
        支持的城市：北京、上海、广州、深圳、杭州、成都、西安、武汉、重庆、天津
        
        如果没有明确的城市名称，返回"北京"作为默认值。
        """
        
        try:
            city_response = await self.llm.ainvoke([HumanMessage(content=extract_prompt)])
            city = city_response.content.strip()
            
            # 调用天气查询工具
            weather_data = await self._call_mcp_tool("get_current_weather", {"city": city})
            state["weather_data"] = weather_data
            
        except Exception as e:
            state["weather_data"] = f"获取天气信息失败: {str(e)}"
        
        return state
    
    async def _generate_response(self, state: AgentState) -> AgentState:
        """生成最终响应"""
        user_input = state.get("user_input", "")
        weather_data = state.get("weather_data", "")
        
        # 构建系统提示
        system_prompt = """
        你是一个专业的航天质量风险管理智能体助手。你可以帮助用户查询天气信息，
        并从航天质量风险管理的角度分析天气对航天活动的影响。
        
        请用友好、专业的语气回答用户的问题。如果涉及天气查询，
        请基于提供的天气数据给出详细的回答，并分析天气条件对航天活动可能的影响。
        """
        
        if weather_data:
            prompt = f"""
            系统提示：{system_prompt}
            
            用户问题：{user_input}
            
            天气数据：{weather_data}
            
            请基于以上天气数据回答用户的问题，并从航天质量风险管理的角度分析天气条件的影响。
            """
        else:
            prompt = f"""
            系统提示：{system_prompt}
            
            用户问题：{user_input}
            
            请回答用户的问题。如果用户询问天气相关信息，请告知用户需要指定具体的城市名称。
            """
        
        try:
            response = await self.llm.ainvoke([HumanMessage(content=prompt)])
            state["response"] = response.content
        except Exception as e:
            state["response"] = f"生成回答时发生错误: {str(e)}"
        
        return state
    
    async def process_message(self, user_input: str) -> str:
        """处理用户消息"""
        try:
            initial_state = {
                "messages": [],
                "user_input": user_input,
                "weather_data": None,
                "response": ""
            }
            
            # 运行工作流
            result = await self.graph.ainvoke(initial_state)
            return result.get("response", "抱歉，我无法处理您的请求。")
            
        except Exception as e:
            return f"处理消息时发生错误: {str(e)}"
    
    async def close(self):
        """关闭资源"""
        if self.mcp_session:
            try:
                await self.mcp_session.__aexit__(None, None, None)
            except:
                pass

# 创建智能体实例的工厂函数
def create_weather_agent(config: WeatherAgentConfig) -> WeatherAgent:
    """创建天气智能体实例"""
    return WeatherAgent(config)
