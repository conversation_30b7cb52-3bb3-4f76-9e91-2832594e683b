#!/usr/bin/env python3
"""
FastHTML UI应用
为航天质量风险管理智能体提供Web界面
"""

import os
import asyncio
from datetime import datetime
from typing import List, Dict, Any

from fasthtml.common import *
from dotenv import load_dotenv

from agent import WeatherAgent, WeatherAgentConfig

# 加载环境变量
load_dotenv()

# 应用配置
app_config = {
    "host": os.getenv("HOST", "127.0.0.1"),
    "port": int(os.getenv("PORT", 8000)),
    "debug": os.getenv("DEBUG", "True").lower() == "true"
}

# 创建FastHTML应用
app, rt = fast_app(
    hdrs=(
        Link(rel="stylesheet", href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"),
        <PERSON><PERSON><PERSON>(src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"),
        Style("""
            .chat-container {
                height: 500px;
                overflow-y: auto;
                border: 1px solid #dee2e6;
                border-radius: 0.375rem;
                padding: 1rem;
                background-color: #f8f9fa;
            }
            .message {
                margin-bottom: 1rem;
                padding: 0.75rem;
                border-radius: 0.5rem;
            }
            .user-message {
                background-color: #007bff;
                color: white;
                margin-left: 20%;
                text-align: right;
            }
            .agent-message {
                background-color: #e9ecef;
                color: #495057;
                margin-right: 20%;
            }
            .timestamp {
                font-size: 0.8rem;
                opacity: 0.7;
                margin-top: 0.25rem;
            }
            .header-bg {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
            }
            .loading {
                display: none;
            }
            .loading.show {
                display: block;
            }
        """)
    )
)

# 全局变量存储聊天历史和智能体实例
chat_history: List[Dict[str, Any]] = []
weather_agent: WeatherAgent = None

def init_agent():
    """初始化智能体"""
    global weather_agent
    
    try:
        config = WeatherAgentConfig(
            openai_api_key=os.getenv("OPENAI_API_KEY", ""),
            openai_base_url=os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1"),
            model_name="gpt-3.5-turbo",
            temperature=0.7
        )
        
        if not config.openai_api_key:
            print("警告: 未设置OPENAI_API_KEY，智能体功能可能受限")
        
        weather_agent = WeatherAgent(config)
        print("智能体初始化成功")
        
    except Exception as e:
        print(f"智能体初始化失败: {e}")

def create_message_div(message: Dict[str, Any]) -> Div:
    """创建消息显示组件"""
    is_user = message["type"] == "user"
    css_class = "user-message" if is_user else "agent-message"
    
    return Div(
        Div(message["content"], cls="message-content"),
        Div(message["timestamp"], cls="timestamp"),
        cls=f"message {css_class}"
    )

def create_chat_interface() -> Div:
    """创建聊天界面"""
    chat_messages = [create_message_div(msg) for msg in chat_history]
    
    return Div(
        # 聊天历史显示区域
        Div(
            *chat_messages,
            id="chat-messages",
            cls="chat-container mb-3"
        ),
        
        # 输入区域
        Form(
            Div(
                Input(
                    type="text",
                    name="message",
                    placeholder="请输入您的问题（例如：北京今天天气怎么样？）",
                    cls="form-control",
                    required=True,
                    autocomplete="off"
                ),
                cls="col-md-10"
            ),
            Div(
                Button(
                    "发送",
                    type="submit",
                    cls="btn btn-primary w-100"
                ),
                cls="col-md-2"
            ),
            cls="row g-2",
            hx_post="/chat",
            hx_target="#chat-messages",
            hx_swap="innerHTML",
            hx_indicator="#loading"
        ),
        
        # 加载指示器
        Div(
            Div(
                Span("正在处理中...", cls="spinner-border spinner-border-sm me-2"),
                "智能体正在思考，请稍候...",
                cls="text-center"
            ),
            id="loading",
            cls="loading mt-3 alert alert-info"
        )
    )

@rt("/")
def homepage():
    """主页"""
    return Html(
        Head(
            Title("航天质量风险管理智能体"),
            Meta(charset="utf-8"),
            Meta(name="viewport", content="width=device-width, initial-scale=1")
        ),
        Body(
            # 页面头部
            Div(
                Div(
                    H1("🚀 航天质量风险管理智能体", cls="text-center mb-0"),
                    P("", cls="text-center mb-0 mt-2"),
                    cls="container py-4"
                ),
                cls="header-bg"
            ),
            
            # 主要内容区域
            Div(
                Div(
                    # 功能介绍
                    Div(
                        H3("功能介绍", cls="mb-3"),
                        Ul(
                            Li("🌤️ 实时天气查询 - 支持全国主要城市"),
                            Li("📊 天气数据分析 - 从航天质量风险管理角度分析"),
                            Li("🔍 智能对话 - 自然语言交互"),
                            Li("⚡ 快速响应 - 基于MCP协议的高效工具调用"),
                            cls="list-unstyled"
                        ),
                        cls="col-md-4 mb-4"
                    ),
                    
                    # 聊天界面
                    Div(
                        H3("智能对话", cls="mb-3"),
                        create_chat_interface(),
                        cls="col-md-8"
                    ),
                    cls="row"
                ),
                cls="container mt-4"
            ),
            
            # 页面底部
            Div(
                Div(
                    P("© 2024 航天质量风险管理智能体 | 基于LangGraph + FastHTML + MCP", 
                      cls="text-center mb-0"),
                    cls="container py-3"
                ),
                cls="bg-light mt-5"
            ),
            
            # JavaScript
            Script("""
                // 自动滚动到聊天底部
                function scrollToBottom() {
                    const chatContainer = document.getElementById('chat-messages');
                    if (chatContainer) {
                        chatContainer.scrollTop = chatContainer.scrollHeight;
                    }
                }
                
                // 页面加载完成后滚动到底部
                document.addEventListener('DOMContentLoaded', scrollToBottom);
                
                // HTMX请求完成后滚动到底部
                document.body.addEventListener('htmx:afterSwap', function(evt) {
                    if (evt.detail.target.id === 'chat-messages') {
                        scrollToBottom();
                        // 清空输入框
                        const input = document.querySelector('input[name="message"]');
                        if (input) input.value = '';
                    }
                });
                
                // 显示/隐藏加载指示器
                document.body.addEventListener('htmx:beforeRequest', function(evt) {
                    document.getElementById('loading').classList.add('show');
                });
                
                document.body.addEventListener('htmx:afterRequest', function(evt) {
                    document.getElementById('loading').classList.remove('show');
                });
            """)
        )
    )

@rt("/chat", methods=["POST"])
async def chat_endpoint(message: str):
    """处理聊天请求"""
    global chat_history, weather_agent
    
    try:
        # 添加用户消息到历史
        user_message = {
            "type": "user",
            "content": message,
            "timestamp": datetime.now().strftime("%H:%M:%S")
        }
        chat_history.append(user_message)
        
        # 获取智能体响应
        if weather_agent:
            agent_response = await weather_agent.process_message(message)
        else:
            agent_response = "抱歉，智能体尚未初始化。请检查配置并重启应用。"
        
        # 添加智能体响应到历史
        agent_message = {
            "type": "agent",
            "content": agent_response,
            "timestamp": datetime.now().strftime("%H:%M:%S")
        }
        chat_history.append(agent_message)
        
        # 返回更新后的聊天消息
        chat_messages = [create_message_div(msg) for msg in chat_history]
        return Div(*chat_messages)
        
    except Exception as e:
        error_message = {
            "type": "agent",
            "content": f"处理请求时发生错误: {str(e)}",
            "timestamp": datetime.now().strftime("%H:%M:%S")
        }
        chat_history.append(error_message)
        
        chat_messages = [create_message_div(msg) for msg in chat_history]
        return Div(*chat_messages)

@rt("/health")
def health_check():
    """健康检查端点"""
    return {"status": "ok", "timestamp": datetime.now().isoformat()}

if __name__ == "__main__":
    # 初始化智能体
    init_agent()
    
    # 添加欢迎消息
    welcome_message = {
        "type": "agent",
        "content": "您好！我是航天质量风险管理智能体。我可以帮您查询天气信息，并从航天质量风险管理的角度分析天气条件的影响。请告诉我您想了解哪个城市的天气情况？",
        "timestamp": datetime.now().strftime("%H:%M:%S")
    }
    chat_history.append(welcome_message)
    
    print(f"启动FastHTML应用...")
    print(f"访问地址: http://{app_config['host']}:{app_config['port']}")
    
    # 启动应用
    import uvicorn
    uvicorn.run(
        app,
        host=app_config["host"],
        port=0  # 让系统自动分配端口
    )
