#!/usr/bin/env python3
"""
航天质量风险管理智能体 - 完整版本

"""

import os
import asyncio
from typing import List, Optional, TypedDict, Annotated
from fasthtml.common import *
import uvicorn

# 导入天气服务器功能
from weather_server import get_current_weather, get_weather_forecast, list_supported_cities

# 导入LangGraph相关
from langgraph.graph import StateGraph, add_messages
from langchain_core.messages import HumanMessage, AIMessage

# 智能体状态定义
class AgentState(TypedDict):
    messages: Annotated[List, add_messages]
    user_input: str
    weather_data: Optional[str]
    response: str

# 创建FastHTML应用
app, rt = fast_app()

# 智能体处理函数
def process_weather_query(state: AgentState) -> AgentState:
    """处理天气查询"""
    user_input = state["user_input"]
    
    # 简单的关键词匹配来确定查询类型
    if "天气" in user_input or "气温" in user_input or "温度" in user_input:
        # 提取城市名称（简单实现）
        cities = ["北京", "上海", "广州", "深圳", "杭州", "南京", "成都", "武汉", "西安", "重庆"]
        city = "北京"  # 默认城市
        for c in cities:
            if c in user_input:
                city = c
                break
        
        if "预报" in user_input or "未来" in user_input:
            weather_data = get_weather_forecast(city)
        else:
            weather_data = get_current_weather(city)
        
        state["weather_data"] = weather_data
        state["response"] = f"为您查询到{city}的天气信息：\n{weather_data}"
    elif "城市" in user_input and "支持" in user_input:
        cities_data = list_supported_cities()
        state["weather_data"] = cities_data
        state["response"] = f"支持查询的城市列表：\n{cities_data}"
    else:
        state["response"] = "您好！我是航天质量风险管理智能体。我可以帮您查询天气信息。请告诉我您想查询哪个城市的天气？"
    
    return state

# 创建智能体图
def create_agent():
    """创建智能体工作流"""
    workflow = StateGraph(AgentState)
    
    # 添加节点
    workflow.add_node("process_query", process_weather_query)
    
    # 设置入口点
    workflow.set_entry_point("process_query")
    
    # 设置结束点
    workflow.set_finish_point("process_query")
    
    return workflow.compile()

# 初始化智能体
agent = create_agent()

@rt("/")
def homepage():
    """主页"""
    return Html(
        Head(
            Title("航天质量风险管理智能体"),
            Meta(charset="utf-8"),
            Meta(name="viewport", content="width=device-width, initial-scale=1"),
            Link(rel="stylesheet", href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"),
            Script(src="https://unpkg.com/htmx.org@1.9.10"),
            Style("""
                .chat-container { max-height: 500px; overflow-y: auto; }
                .message { margin-bottom: 10px; padding: 10px; border-radius: 10px; }
                .user-message { background-color: #007bff; color: white; margin-left: 20%; }
                .ai-message { background-color: #f8f9fa; margin-right: 20%; }
                .loading { display: none; }
            """)
        ),
        Body(
            Div(
                H1("🚀 航天质量风险管理智能体", cls="text-center mb-4"),
                P("", cls="text-center text-muted mb-4"),
                
                # 聊天容器
                Div(id="chat-container", cls="chat-container border p-3 mb-3"),
                
                # 输入表单
                Form(
                    Div(
                        Input(
                            type="text", 
                            name="message", 
                            placeholder="请输入您的问题，例如：北京天气如何？", 
                            cls="form-control",
                            required=True
                        ),
                        cls="input-group mb-2"
                    ),
                    Button("发送", type="submit", cls="btn btn-primary"),
                    Div("正在处理中...", cls="loading text-muted mt-2"),
                    hx_post="/chat",
                    hx_target="#chat-container",
                    hx_swap="beforeend",
                    hx_indicator=".loading"
                ),
                
                # 示例查询
                Div(
                    H5("示例查询："),
                    Ul(
                        Li("北京天气如何？"),
                        Li("上海未来几天天气预报"),
                        Li("支持查询哪些城市？"),
                        Li("广州的气温是多少？")
                    ),
                    cls="mt-4"
                ),
                
                cls="container mt-4"
            ),
            Script(src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js")
        )
    )

@rt("/chat", methods=["POST"])
def chat(message: str):
    """处理聊天请求"""
    try:
        # 创建初始状态
        initial_state = {
            "messages": [HumanMessage(content=message)],
            "user_input": message,
            "weather_data": None,
            "response": ""
        }
        
        # 运行智能体
        result = agent.invoke(initial_state)
        
        # 返回聊天消息
        return Div(
            Div(message, cls="message user-message"),
            Div(result["response"], cls="message ai-message"),
            style="white-space: pre-wrap;"
        )
        
    except Exception as e:
        return Div(
            Div(message, cls="message user-message"),
            Div(f"抱歉，处理您的请求时出现错误：{str(e)}", cls="message ai-message text-danger")
        )

if __name__ == "__main__":
    print("🚀 启动航天质量风险管理智能体...")
    print("📡 ")
    print("🌐 访问地址: http://127.0.0.1:0 (系统自动分配端口)")
    
    uvicorn.run(app, host="127.0.0.1", port=0)
