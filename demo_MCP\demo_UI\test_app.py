#!/usr/bin/env python3
"""
简化版本的FastHTML应用测试
"""

from fasthtml.common import *

# 创建FastHTML应用
app, rt = fast_app()

@rt("/")
def homepage():
    """主页"""
    return Html(
        Head(Title("航天质量风险管理智能体")),
        Body(
            H1("🚀 航天质量风险管理智能体"),
            P(""),
            P("应用正在运行中...")
        )
    )

if __name__ == "__main__":
    print("启动测试应用...")
    print("访问地址: http://127.0.0.1:8000")
    serve(host="127.0.0.1", port=8000)
